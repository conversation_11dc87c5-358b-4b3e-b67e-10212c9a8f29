# StorageService 重试功能配置模板
# 使用说明：复制此配置到你的 application.yml 或 application-{profile}.yml 文件中

# ==================== S3 存储配置 ====================
s3:
  # 基础连接配置
  endpoint: ${S3_ENDPOINT:http://localhost:9000}
  accessKey: ${S3_ACCESS_KEY:minioadmin}
  secretKey: ${S3_SECRET_KEY:minioadmin}
  region: ${S3_REGION:us-east-1}
  bucketName: ${S3_BUCKET_NAME:naas-storage}
  https: ${S3_HTTPS:false}
  
  # 连接池配置
  maxConnections: ${S3_MAX_CONNECTIONS:200}
  connectionTimeout: ${S3_CONNECTION_TIMEOUT:10000}
  socketTimeout: ${S3_SOCKET_TIMEOUT:50000}
  requestTimeout: ${S3_REQUEST_TIMEOUT:300000}
  maxRetries: ${S3_MAX_RETRIES:3}
  
  # ==================== 重试配置 ====================
  retry:
    # 默认重试配置（适用于所有操作）
    default:
      maxRetries: ${S3_RETRY_MAX_RETRIES:3}           # 最大重试次数
      baseDelay: ${S3_RETRY_BASE_DELAY:1000}          # 基础延迟时间(毫秒)
      maxDelay: ${S3_RETRY_MAX_DELAY:30000}           # 最大延迟时间(毫秒)
      strategy: ${S3_RETRY_STRATEGY:EXPONENTIAL_BACKOFF}  # 重试策略
      enableJitter: ${S3_RETRY_ENABLE_JITTER:true}    # 启用抖动
      jitterFactor: ${S3_RETRY_JITTER_FACTOR:0.1}     # 抖动因子
    
    # 按操作类型的特定配置
    operationSpecific:
      # 上传操作配置
      upload:
        maxRetries: ${S3_UPLOAD_MAX_RETRIES:5}
        baseDelay: ${S3_UPLOAD_BASE_DELAY:2000}
        maxDelay: ${S3_UPLOAD_MAX_DELAY:60000}
        strategy: ${S3_UPLOAD_STRATEGY:EXPONENTIAL_BACKOFF}
        enableJitter: true
        jitterFactor: 0.15
      
      # 下载操作配置
      download:
        maxRetries: ${S3_DOWNLOAD_MAX_RETRIES:3}
        baseDelay: ${S3_DOWNLOAD_BASE_DELAY:1000}
        maxDelay: ${S3_DOWNLOAD_MAX_DELAY:30000}
        strategy: ${S3_DOWNLOAD_STRATEGY:LINEAR_BACKOFF}
        enableJitter: true
        jitterFactor: 0.1
      
      # 元数据操作配置
      metadata:
        maxRetries: ${S3_METADATA_MAX_RETRIES:2}
        baseDelay: ${S3_METADATA_BASE_DELAY:500}
        maxDelay: ${S3_METADATA_MAX_DELAY:10000}
        strategy: ${S3_METADATA_STRATEGY:FIXED_DELAY}
        enableJitter: false
        jitterFactor: 0.0

# ==================== 环境配置示例 ====================

# 开发环境配置示例
---
spring:
  profiles: dev
s3:
  retry:
    default:
      maxRetries: 2
      baseDelay: 500
      strategy: FIXED_DELAY
    operationSpecific:
      upload:
        maxRetries: 3
        baseDelay: 1000

---
# 测试环境配置示例
spring:
  profiles: test
s3:
  retry:
    default:
      maxRetries: 3
      baseDelay: 1000
      strategy: LINEAR_BACKOFF
    operationSpecific:
      upload:
        maxRetries: 4
        baseDelay: 1500

---
# 生产环境配置示例
spring:
  profiles: prod
s3:
  retry:
    default:
      maxRetries: 5
      baseDelay: 2000
      strategy: EXPONENTIAL_BACKOFF
      enableJitter: true
    operationSpecific:
      upload:
        maxRetries: 8
        baseDelay: 3000
        maxDelay: 120000
      download:
        maxRetries: 5
        baseDelay: 2000

# ==================== 监控配置 ====================
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,storage-retry-metrics
  endpoint:
    storage-retry-metrics:
      enabled: true
    health:
      show-details: when-authorized

# ==================== 日志配置 ====================
logging:
  level:
    cn.cmcc.common.storage: INFO
    cn.cmcc.common.storage.service.impl.StorageService: DEBUG
    cn.cmcc.common.storage.service.EnhancedRetryExecutor: DEBUG

# ==================== 配置说明 ====================
# 
# 重试策略说明：
# - EXPONENTIAL_BACKOFF: 指数退避，延迟时间呈指数增长 (推荐用于网络错误)
# - LINEAR_BACKOFF: 线性退避，延迟时间线性增长 (适用于服务繁忙)
# - FIXED_DELAY: 固定延迟，每次重试使用相同延迟 (适用于快速重试)
# - ADAPTIVE: 自适应策略，根据历史成功率动态调整 (智能场景)
#
# 抖动(Jitter)说明：
# - 启用抖动可以避免多个请求同时重试造成的"雷群效应"
# - jitterFactor 控制抖动幅度，建议值 0.1-0.2
#
# 环境变量优先级：
# - 环境变量 > 配置文件 > 默认值
# - 生产环境建议使用环境变量配置敏感信息
#
# 监控端点：
# - /actuator/storage-retry-metrics: 查看重试统计信息
# - /actuator/health: 查看服务健康状态
# - /actuator/metrics: 查看系统指标
