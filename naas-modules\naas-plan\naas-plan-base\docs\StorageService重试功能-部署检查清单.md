# StorageService 重试功能 - 部署检查清单

## 📋 **部署前检查**

### **1. 环境准备**
- [ ] **Java版本**: JDK 8+ 或 JDK 11+
- [ ] **Spring Boot版本**: 2.3.0+ 
- [ ] **Maven版本**: 3.6.0+
- [ ] **S3服务**: MinIO 或 AWS S3 可访问

### **2. 依赖检查**
- [ ] **naas-common-storage**: 已更新到最新版本
- [ ] **AWS SDK**: com.amazonaws:aws-java-sdk-s3
- [ ] **Spring Boot Actuator**: 用于监控端点
- [ ] **Lombok**: 用于代码简化

### **3. 配置文件准备**
- [ ] **主配置**: application.yml 已更新
- [ ] **环境配置**: application-{profile}.yml 已配置
- [ ] **重试配置**: s3.retry 配置段已添加
- [ ] **监控配置**: management.endpoints 已启用

---

## 🚀 **部署步骤**

### **Step 1: 备份现有配置**
```bash
# 备份配置文件
cp application.yml application.yml.backup.$(date +%Y%m%d_%H%M%S)
cp application-prod.yml application-prod.yml.backup.$(date +%Y%m%d_%H%M%S)
```
- [ ] 配置文件已备份

### **Step 2: 更新配置文件**
```bash
# 合并新的重试配置到现有配置文件
# 参考: docs/application-storage-retry-template.yml
```
- [ ] S3基础配置已更新
- [ ] 重试配置已添加
- [ ] 监控端点已启用
- [ ] 日志级别已配置

### **Step 3: 环境变量设置**
```bash
# 生产环境推荐环境变量
export S3_RETRY_MAX_RETRIES=5
export S3_RETRY_BASE_DELAY=2000
export S3_UPLOAD_MAX_RETRIES=8
export S3_DOWNLOAD_MAX_RETRIES=5
```
- [ ] 环境变量已设置
- [ ] 敏感信息使用环境变量

### **Step 4: 编译部署**
```bash
# 编译项目
mvn clean compile -DskipTests

# 运行测试（可选）
mvn test -Dtest=StorageServiceUnitTest

# 打包部署
mvn clean package -DskipTests
```
- [ ] 编译成功
- [ ] 测试通过（如果运行）
- [ ] 打包成功

### **Step 5: 服务启动**
```bash
# 启动服务
java -jar naas-plan-service.jar --spring.profiles.active=prod

# 或使用systemctl
systemctl start naas-plan-service
```
- [ ] 服务启动成功
- [ ] 无启动错误日志

---

## ✅ **部署后验证**

### **1. 基础功能验证**
```bash
# 检查服务状态
curl http://localhost:8080/actuator/health
# 预期返回: {"status":"UP"}
```
- [ ] 服务健康检查通过

### **2. 重试功能验证**
```bash
# 查看重试配置
curl http://localhost:8080/actuator/storage-retry-metrics
# 预期返回: JSON格式的重试配置和统计信息
```
- [ ] 重试配置正确加载
- [ ] 监控端点正常响应

### **3. S3连接验证**
```bash
# 检查S3连接（通过应用日志）
tail -f /var/log/naas-plan.log | grep "S3客户端初始化完成"
```
- [ ] S3客户端初始化成功
- [ ] 存储桶连接正常

### **4. 功能测试**
- [ ] **文件上传测试**: 上传一个小文件，验证成功
- [ ] **文件下载测试**: 下载已上传的文件，验证成功
- [ ] **重试测试**: 模拟网络异常，观察重试行为

---

## 📊 **监控设置**

### **1. 监控端点配置**
```bash
# 验证监控端点
curl http://localhost:8080/actuator/storage-retry-metrics
curl http://localhost:8080/actuator/metrics
```
- [ ] storage-retry-metrics 端点可访问
- [ ] 返回正确的JSON格式数据

### **2. 日志监控**
```bash
# 设置日志监控
tail -f /var/log/naas-plan.log | grep -E "重试|retry|Retry"
```
- [ ] 重试相关日志正常输出
- [ ] 日志格式清晰可读

### **3. 告警配置**
- [ ] **成功率告警**: < 80% 时触发
- [ ] **重试次数告警**: > 1000次/小时 时触发
- [ ] **平均耗时告警**: > 5秒 时触发

---

## 🔧 **故障排查**

### **常见问题检查**

#### **配置问题**
```bash
# 检查配置是否正确加载
curl http://localhost:8080/actuator/configprops | grep -i retry
```
- [ ] 重试配置正确加载

#### **网络问题**
```bash
# 检查S3服务连通性
ping your-s3-endpoint
telnet your-s3-endpoint 9000
```
- [ ] S3服务网络连通
- [ ] 端口正常开放

#### **权限问题**
```bash
# 检查S3访问权限
# 通过应用日志查看是否有权限错误
grep -i "access denied\|forbidden" /var/log/naas-plan.log
```
- [ ] S3访问权限正常

---

## 📈 **性能优化**

### **1. 连接池优化**
- [ ] **maxConnections**: 根据并发量调整（建议200-500）
- [ ] **connectionTimeout**: 网络环境调整（建议10-30秒）
- [ ] **socketTimeout**: 传输超时调整（建议30-60秒）

### **2. 重试参数优化**
- [ ] **maxRetries**: 根据网络稳定性调整（建议3-8次）
- [ ] **baseDelay**: 根据响应时间调整（建议1-3秒）
- [ ] **strategy**: 根据错误类型选择合适策略

### **3. JVM参数优化**
```bash
# 推荐JVM参数
-Xms2g -Xmx4g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
```
- [ ] JVM内存参数已优化
- [ ] GC参数已调整

---

## 📞 **支持联系**

### **部署支持**
- **技术负责人**: Claude 4.0 sonnet
- **部署日期**: ___________
- **部署人员**: ___________
- **环境**: [ ] 开发 [ ] 测试 [ ] 生产

### **验收签字**
- **开发确认**: ___________  日期: ___________
- **测试确认**: ___________  日期: ___________
- **运维确认**: ___________  日期: ___________

---

## 📝 **备注**
```
部署过程中的特殊情况记录：

1. 

2. 

3. 

```

**✅ 部署完成确认**: 所有检查项已完成，系统运行正常
