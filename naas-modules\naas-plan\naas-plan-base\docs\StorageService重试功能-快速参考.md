# StorageService 重试功能 - 快速参考卡片

## 🚀 **一分钟了解**

### **功能简介**
为 StorageService 添加智能重试机制，提升文件上传下载的可靠性。

### **核心特性**
- ✅ 自动重试失败的上传/下载操作
- 🔄 4种重试策略：指数退避、线性退避、固定延迟、自适应
- 📊 完整的监控指标和实时统计
- ⚙️ 灵活的分层配置系统

---

## ⚙️ **快速配置**

### **基础配置**
```yaml
s3:
  retry:
    default:
      maxRetries: 3                    # 最大重试3次
      baseDelay: 1000                  # 基础延迟1秒
      strategy: EXPONENTIAL_BACKOFF    # 指数退避策略
```

### **环境变量**
```bash
S3_RETRY_MAX_RETRIES=5      # 生产环境建议5次
S3_RETRY_BASE_DELAY=2000    # 生产环境建议2秒
```

---

## 📊 **监控检查**

### **健康检查**
```bash
# 查看重试指标
curl http://localhost:8080/actuator/storage-retry-metrics

# 检查服务状态
curl http://localhost:8080/actuator/health
```

### **关键指标**
- `globalSuccessRate`: 全局成功率 (建议 > 80%)
- `totalRetries`: 总重试次数
- `averageDuration`: 平均重试耗时

---

## 🔧 **故障排查**

### **常见问题**
| 问题 | 检查项 | 解决方案 |
|------|--------|----------|
| 重试未生效 | 配置是否正确 | 检查 YAML 格式 |
| 重试过多 | 网络连接 | 检查 S3 服务状态 |
| 响应缓慢 | 重试参数 | 调整延迟时间 |

### **快速诊断**
```bash
# 检查网络连通性
ping your-s3-endpoint

# 查看重试日志
grep "重试" /var/log/naas-plan.log | tail -20
```

---

## 📈 **推荐配置**

### **开发环境**
```yaml
s3:
  retry:
    default:
      maxRetries: 2
      baseDelay: 500
      strategy: FIXED_DELAY
```

### **生产环境**
```yaml
s3:
  retry:
    default:
      maxRetries: 5
      baseDelay: 2000
      strategy: EXPONENTIAL_BACKOFF
      enableJitter: true
```

---

## 🚨 **告警阈值**

| 指标 | 告警阈值 | 级别 |
|------|----------|------|
| 成功率 | < 80% | Warning |
| 重试次数 | > 1000/小时 | Critical |
| 平均耗时 | > 5秒 | Warning |

---

## 📞 **紧急联系**

- **技术支持**: Claude 4.0 sonnet
- **更新日期**: 2025-01-30
- **详细文档**: [StorageService重试功能运维说明.md](./StorageService重试功能运维说明.md)
